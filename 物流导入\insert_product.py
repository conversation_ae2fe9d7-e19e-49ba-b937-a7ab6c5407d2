import pandas as pd
import logging
import os
from sqlalchemy import create_engine
from sqlalchemy.exc import SQLAlchemyError
import urllib.parse

# ==================== 配置 ====================
excel_file_path = 'import_logistics_product_yanwen.xlsx'
db_config = {
    'host': '*************',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'ruoyi-vue-pro',
}

# ==================== 日志 ====================
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# ==================== 转义密码（如果包含特殊字符）====================
password_encoded = urllib.parse.quote_plus(db_config['password'])
connection_url = (
    f"mysql+pymysql://{db_config['user']}:{password_encoded}"
    f"@{db_config['host']}:{db_config['port']}/{db_config['database']}"
    f"?connect_timeout=10&charset=utf8mb4"
)

engine = create_engine(connection_url, echo=False)

# ==================== 读取 Excel ====================
# ==================== 读取 Excel ====================
if not os.path.exists(excel_file_path):
    logger.error(f"❌ 文件不存在: {excel_file_path}")
    exit(1)

try:
    df = pd.read_excel(excel_file_path, header=1, dtype=str)
    df.dropna(how='all', inplace=True)
    logger.info(f"✅ 读取 Excel 成功，共 {len(df)} 行")

    # ======== 数据清洗：'是'/'否' → 1/0 ========
    boolean_columns = [
        'electronic', 'cosmetic', 'clothing', 'large',
        'recommend', 'tax_include', 'need_volume_cal',
        'volume_base', 'free_insure', 'ioss_enabled', 'recommended'
    ]

    def convert_yes_no(value):
        if pd.isna(value):
            return 0
        v = str(value).strip()
        if v in ['是', 'YES', 'Yes', 'yes', 'Y', '1']:
            return 1
        return 0  # 默认为 0（'否'）

    for col in boolean_columns:
        if col in df.columns:
            df[col] = df[col].apply(convert_yes_no)
            logger.info(f"🔄 已转换列 '{col}' 中的 是/否 → 1/0")

except Exception as e:
    logger.error(f"❌ 读取或清洗数据失败: {e}")
    exit(1)

# ==================== 写入数据库 ====================
try:
    logger.info("📤 正在连接并写入数据库...")
    with engine.connect() as conn:
        df.to_sql(
            name='agent_logistics_product',
            con=conn,
            if_exists='append',
            index=False,
            chunksize=100,
            method='multi'
        )
    logger.info(f"✅ 数据写入成功！共 {len(df)} 条")
except SQLAlchemyError as e:
    logger.error(f"❌ SQLAlchemy 错误: {e}")
    if "timeout" in str(e).lower():
        logger.error("👉 网络连接超时，请检查：IP、端口、防火墙、MySQL 是否允许远程连接")
except Exception as e:
    logger.error(f"❌ 未知错误: {e}")