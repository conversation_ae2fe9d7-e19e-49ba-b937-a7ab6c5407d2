// 公共工具函数和数据

// 完整的国家编码映射表
const COUNTRY_MAP = {
  // ========== 亚洲 ==========
  中国: "CN",
  日本: "JP",
  韩国: "KR",
  大韩民国: "KR",
  朝鲜: "KP",
  蒙古: "MN",
  新加坡: "SG",
  马来西亚: "MY",
  泰国: "TH",
  菲律宾: "PH",
  越南: "VN",
  老挝: "LA",
  柬埔寨: "KH",
  缅甸: "MM",
  印度尼西亚: "ID",
  文莱: "BN",
  东帝汶: "TL",
  印度: "IN",
  巴基斯坦: "PK",
  孟加拉国: "BD",
  斯里兰卡: "LK",
  马尔代夫: "MV",
  尼泊尔: "NP",
  不丹: "BT",
  阿富汗: "AF",
  伊朗: "IR",
  伊拉克: "IQ",
  叙利亚: "SY",
  黎巴嫩: "LB",
  约旦: "JO",
  以色列: "IL",
  巴勒斯坦: "PS",
  沙特阿拉伯: "SA",
  阿联酋: "AE",
  阿拉伯联合酋长国: "AE",
  卡塔尔: "QA",
  科威特: "KW",
  巴林: "BH",
  阿曼: "OM",
  也门: "YE",
  土耳其: "TR",
  塞浦路斯: "CY",
  格鲁吉亚: "GE",
  亚美尼亚: "AM",
  阿塞拜疆: "AZ",
  哈萨克斯坦: "KZ",
  吉尔吉斯斯坦: "KG",
  塔吉克斯坦: "TJ",
  土库曼斯坦: "TM",
  乌兹别克斯坦: "UZ",

  // ========== 欧洲 ==========
  英国: "GB",
  大不列颠: "GB",
  联合王国: "GB",
  爱尔兰: "IE",
  法国: "FR",
  法兰西: "FR",
  德国: "DE",
  德意志: "DE",
  意大利: "IT",
  西班牙: "ES",
  葡萄牙: "PT",
  荷兰: "NL",
  尼德兰: "NL",
  比利时: "BE",
  卢森堡: "LU",
  瑞士: "CH",
  奥地利: "AT",
  列支敦士登: "LI",
  丹麦: "DK",
  瑞典: "SE",
  挪威: "NO",
  芬兰: "FI",
  冰岛: "IS",
  波兰: "PL",
  捷克: "CZ",
  捷克共和国: "CZ",
  斯洛伐克: "SK",
  匈牙利: "HU",
  斯洛文尼亚: "SI",
  克罗地亚: "HR",
  波黑: "BA",
  塞尔维亚: "RS",
  黑山: "ME",
  北马其顿: "MK",
  阿尔巴尼亚: "AL",
  希腊: "GR",
  保加利亚: "BG",
  罗马尼亚: "RO",
  摩尔多瓦: "MD",
  乌克兰: "UA",
  白俄罗斯: "BY",
  立陶宛: "LT",
  拉脱维亚: "LV",
  爱沙尼亚: "EE",
  俄罗斯: "RU",
  俄国: "RU",
  马耳他: "MT",
  摩纳哥: "MC",
  安道尔: "AD",
  圣马力诺: "SM",
  梵蒂冈: "VA",
  俄罗斯联邦: "RU",
  特克斯和凯科斯群岛: "TC",
  安圭拉: "AI",

  // ========== 北美洲 ==========
  美国: "US",
  合众国: "US",
  加拿大: "CA",
  墨西哥: "MX",
  危地马拉: "GT",
  伯利兹: "BZ",
  萨尔瓦多: "SV",
  洪都拉斯: "HN",
  尼加拉瓜: "NI",
  哥斯达黎加: "CR",
  巴拿马: "PA",
  美属萨摩亚: "AS",

  // ========== 南美洲 ==========
  巴西: "BR",
  阿根廷: "AR",
  智利: "CL",
  秘鲁: "PE",
  厄瓜多尔: "EC",
  哥伦比亚: "CO",
  委内瑞拉: "VE",
  圭亚那: "GY",
  苏里南: "SR",
  法属圭亚那: "GF",
  乌拉圭: "UY",
  巴拉圭: "PY",
  玻利维亚: "BO",

  // ========== 大洋洲 ==========
  澳大利亚: "AU",
  澳洲: "AU",
  新西兰: "NZ",
  斐济: "FJ",
  巴布亚新几内亚: "PG",
  所罗门群岛: "SB",
  瓦努阿图: "VU",
  新喀里多尼亚: "NC",
  法属波利尼西亚: "PF",
  萨摩亚: "WS",
  汤加: "TO",
  基里巴斯: "KI",
  图瓦卢: "TV",
  瑙鲁: "NR",
  帕劳: "PW",
  马绍尔群岛: "MH",
  密克罗尼西亚: "FM",

  // ========== 非洲 ==========
  埃及: "EG",
  利比亚: "LY",
  突尼斯: "TN",
  阿尔及利亚: "DZ",
  摩洛哥: "MA",
  苏丹: "SD",
  南苏丹: "SS",
  埃塞俄比亚: "ET",
  厄立特里亚: "ER",
  吉布提: "DJ",
  索马里: "SO",
  肯尼亚: "KE",
  乌干达: "UG",
  坦桑尼亚: "TZ",
  卢旺达: "RW",
  布隆迪: "BI",
  刚果民主共和国: "CD",
  刚果: "CG",
  中非: "CF",
  喀麦隆: "CM",
  赤道几内亚: "GQ",
  加蓬: "GA",
  圣多美和普林西比: "ST",
  尼日利亚: "NG",
  尼日尔: "NE",
  乍得: "TD",
  马里: "ML",
  布基纳法索: "BF",
  塞内加尔: "SN",
  冈比亚: "GM",
  几内亚比绍: "GW",
  几内亚: "GN",
  塞拉利昂: "SL",
  利比里亚: "LR",
  科特迪瓦: "CI",
  加纳: "GH",
  多哥: "TG",
  贝宁: "BJ",
  毛里塔尼亚: "MR",
  西撒哈拉: "EH",
  南非: "ZA",
  纳米比亚: "NA",
  博茨瓦纳: "BW",
  津巴布韦: "ZW",
  赞比亚: "ZM",
  马拉维: "MW",
  莫桑比克: "MZ",
  斯威士兰: "SZ",
  莱索托: "LS",
  马达加斯加: "MG",
  毛里求斯: "MU",
  塞舌尔: "SC",
  科摩罗: "KM",
  马约特: "YT",
  留尼汪: "RE",
  安哥拉: "AO",

  // ========== 加勒比海地区 ==========
  古巴: "CU",
  牙买加: "JM",
  海地: "HT",
  多米尼加: "DO",
  巴哈马: "BS",
  巴巴多斯: "BB",
  特立尼达和多巴哥: "TT",
  圣卢西亚: "LC",
  格林纳达: "GD",
  圣文森特和格林纳丁斯: "VC",
  安提瓜和巴布达: "AG",
  多米尼克: "DM",
  圣基茨和尼维斯: "KN",

  // ========== 其他 ==========
  库拉索岛: "CW",
  泽西岛: "JE",
  马其顿: "MK",
  阿鲁巴: "AW",
  刚果金: "CD",
  刚果布: "CG",
  库克群岛: "CK",
  法罗群岛: "FO",
  直布罗陀: "GI",
  格陵兰: "GL",
  马恩岛: "IM",
  波多黎各: "PR",
  百慕大: "BM",
  佛得角: "CV",
  荷兰加勒比区: "BQ",
  开曼群岛: "KY",
  多米尼加共和国: "DO",
  福克兰群岛: "FK",
  圣马丁: "SX",
  托克劳: "TK",
  瓦利斯和富图纳: "WF",
  北马里亚纳群岛: "MP",
  马提尼克: "MQ",
  圣皮埃尔和密克隆: "PM",
  美属维尔京群岛: "VI",
  格恩西岛: "GG",
};

// 提取时效的工具函数
function extractTimeEffect(timeStr) {
  if (!timeStr) return "3-5";

  // 匹配第一个数字范围，例如：
  // "8-15工作日" -> "8-15"
  // "8-10工作日（东马9-12工作日）" -> "8-10"
  // "7-9个工作日" -> "7-9"
  const match = timeStr.match(/(\d+)-(\d+)/);
  if (match) {
    return `${match[1]}-${match[2]}`;
  }

  // 如果只有单个数字，例如："5工作日"
  const singleMatch = timeStr.match(/(\d+)/);
  if (singleMatch) {
    const days = parseInt(singleMatch[1]);
    return `${days}-${days + 2}`; // 给一个范围
  }

  return "3-5"; // 默认时效
}

// 复制到剪贴板的工具函数
function copyToClipboard(text, messageCallback) {
  if (navigator.clipboard && window.isSecureContext) {
    // 现代浏览器的方法
    navigator.clipboard
      .writeText(text)
      .then(() => {
        if (messageCallback) {
          messageCallback({
            message: "已复制到剪贴板",
            type: "success",
            duration: 1500,
          });
        }
      })
      .catch(() => {
        // 降级到旧方法
        fallbackCopyTextToClipboard(text, messageCallback);
      });
  } else {
    // 降级到旧方法
    fallbackCopyTextToClipboard(text, messageCallback);
  }
}

// 降级的复制方法
function fallbackCopyTextToClipboard(text, messageCallback) {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  textArea.style.position = "fixed";
  textArea.style.left = "-999999px";
  textArea.style.top = "-999999px";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    document.execCommand("copy");
    if (messageCallback) {
      messageCallback({
        message: "已复制到剪贴板",
        type: "success",
        duration: 1500,
      });
    }
  } catch (err) {
    if (messageCallback) {
      messageCallback({
        message: "复制失败，请手动复制",
        type: "error",
        duration: 2000,
      });
    }
  }

  document.body.removeChild(textArea);
}

// 验证数据行的工具函数
function validateDataLine(parts, requiredFields, lineNumber) {
  const errors = [];

  if (parts.length < requiredFields.length) {
    errors.push(
      `第${lineNumber}行：字段数量不足，需要${requiredFields.length}个字段，实际${parts.length}个`
    );
    return errors;
  }

  requiredFields.forEach((field, index) => {
    const value = parts[index] ? parts[index].trim() : "";

    if (field.required && !value) {
      errors.push(`第${lineNumber}行：${field.name}不能为空`);
    }

    if (field.type === "number" && value && isNaN(parseFloat(value))) {
      errors.push(`第${lineNumber}行：${field.name}必须是数字`);
    }

    if (
      field.type === "weight_range" &&
      value &&
      !value.match(/([\d.]+)[\s＜<≤<=]+W?[\s≤<=]+([\d.]+)/)
    ) {
      errors.push(
        `第${lineNumber}行：${field.name}格式不正确，应为"0＜W≤1"格式`
      );
    }
  });

  return errors;
}

// 格式化错误信息
function formatErrors(errors) {
  if (errors.length === 0) return "";

  return "数据验证错误：\n" + errors.join("\n");
}
