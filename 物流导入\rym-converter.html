<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RYM格式转换</title>
    
    <!-- 引入 Element UI 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css" />
    <style>
        body {
            font-family: "Helvetica Neue", Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            min-height: 100vh;
        }
        .container {
            max-width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 25px;
        }
        .header h3 {
            color: #2c3e50;
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .input-area {
            margin-bottom: 20px;
        }
        .result-box {
            margin-top: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            margin: 0;
            font-family: 'Consolas', 'Monaco', monospace;
        }
        .example-text {
            color: #6c757d;
            font-size: 13px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #dc3545;
        }
        .button-group {
            margin: 15px 0;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .error-box {
            margin-top: 15px;
            padding: 15px;
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            color: #c53030;
            white-space: pre-line;
        }
    </style>
</head>
<body>
    <div id="app" class="container">
        <div class="header">
            <h3>🎯 RYM格式转换</h3>
        </div>
        
        <div class="example-text">
            📋 示例格式：目的地 | 重量(千克) | 操作费RMB/票 | 单价RMB/KG | 末端派送参考时效(工作日)
        </div>
        
        <div class="input-area">
            <el-input
                type="textarea"
                :rows="8"
                placeholder="请粘贴RYM Excel数据，包含表头行"
                v-model="rymInput"
            ></el-input>
        </div>
        
        <div class="button-group">
            <el-button type="primary" @click="convertRym" :loading="loading">
                <i class="el-icon-refresh"></i> 转换
            </el-button>
            <el-button @click="clearRym">
                <i class="el-icon-delete"></i> 清空
            </el-button>
            <el-button @click="copyRymResult" v-if="rymResult" type="success">
                <i class="el-icon-document-copy"></i> 复制结果
            </el-button>
        </div>
        
        <div class="error-box" v-if="errors">
            {{ errors }}
        </div>
        
        <div class="result-box" v-if="rymResult">
            <pre>{{ rymResult }}</pre>
        </div>
    </div>
    
    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="common.js"></script>
    
    <script>
        new Vue({
            el: "#app",
            data() {
                return {
                    loading: false,
                    errors: "",
                    rymInput: `目的地	重量 (千克)	操作费RMB/票	单价 RMB/KG	末端派送参考时效（工作日）
澳大利亚	邮编一区	26	65	8-12个工作日
	邮编二区	33	65	
	邮编三区	54	67	
英国	0.05-0.3	21	65	7-9个工作日
	0.301-1	21	65	
	1.001-2	21	68	
德国	0.05-1	30	68	DHL Paket 7-9`,
                    rymResult: ""
                };
            },
            methods: {
                convertRym() {
                    this.loading = true;
                    this.errors = "";
                    this.rymResult = "";
                    
                    try {
                        const lines = this.rymInput.trim().split("\n");
                        if (lines.length === 0) {
                            this.$message({ message: "请输入数据", type: "warning" });
                            return;
                        }
                        
                        // 跳过表头行
                        const dataLines = lines.slice(1);
                        const errors = [];
                        const countryData = {};
                        let currentCountry = "";
                        let currentTimeEffect = "";
                        
                        for (let i = 0; i < dataLines.length; i++) {
                            const line = dataLines[i];
                            const parts = line.trim().split(/\t/);
                            const lineNumber = i + 2;
                            
                            if (parts.length < 4) {
                                errors.push(`第${lineNumber}行：字段数量不足，需要至少4个字段`);
                                continue;
                            }
                            
                            let country = parts[0].trim();
                            const weightOrZone = parts[1].trim();
                            const operationFee = parseFloat(parts[2]);
                            const unitPrice = parseFloat(parts[3]);
                            const timeEffect = parts[4] ? parts[4].trim() : "";
                            
                            // 如果第一列为空，使用上一个国家
                            if (!country && currentCountry) {
                                country = currentCountry;
                            } else if (country) {
                                currentCountry = country;
                                if (timeEffect) {
                                    currentTimeEffect = extractTimeEffect(timeEffect);
                                }
                            }
                            
                            if (!country || isNaN(operationFee) || isNaN(unitPrice)) {
                                errors.push(`第${lineNumber}行：必填字段缺失或格式错误`);
                                continue;
                            }
                            
                            const countryCode = COUNTRY_MAP[country] || country;
                            if (!COUNTRY_MAP[country]) {
                                errors.push(`第${lineNumber}行：未识别的国家名称"${country}"`);
                            }
                            
                            let zone = "";
                            let startWeight = 50; // 默认50g
                            let endWeight = 30000; // 默认30kg
                            
                            // 判断是分区还是重量范围
                            if (weightOrZone.includes("-")) {
                                // 重量范围格式 (例如: "0.05-0.3")
                                const match = weightOrZone.match(/([\d.]+)-([\d.]+)/);
                                if (match) {
                                    startWeight = Math.round(parseFloat(match[1]) * 1000);
                                    endWeight = Math.round(parseFloat(match[2]) * 1000);
                                } else {
                                    errors.push(`第${lineNumber}行：重量范围格式不正确，应为"0.05-0.3"格式`);
                                    continue;
                                }
                            } else {
                                // 分区格式 (例如: "邮编一区")
                                zone = weightOrZone;
                            }
                            
                            const key = zone ? `${countryCode}_${zone}` : countryCode;
                            
                            if (!countryData[key]) {
                                countryData[key] = {
                                    countryCode: countryCode,
                                    zone: zone,
                                    timeEffect: currentTimeEffect || "7-10",
                                    minWeight: startWeight,
                                    maxWeight: endWeight,
                                    tiers: []
                                };
                            }
                            
                            // 更新最小和最大重量
                            countryData[key].minWeight = Math.min(countryData[key].minWeight, startWeight);
                            countryData[key].maxWeight = Math.max(countryData[key].maxWeight, endWeight);
                            
                            // 添加阶梯价格
                            const tierItem = {
                                tierStart: startWeight,
                                tierEnd: endWeight,
                                unitPrice: Math.round(unitPrice * 100), // 转换为分
                                registrationFee: Math.round(operationFee * 100) // 操作费作为挂号费
                            };
                            
                            countryData[key].tiers.push(tierItem);
                        }
                        
                        if (errors.length > 0) {
                            this.errors = formatErrors(errors);
                        }
                        
                        // 生成输出
                        let output = "";
                        for (const [key, data] of Object.entries(countryData)) {
                            // 修正阶梯边界
                            data.tiers.sort((a, b) => a.tierStart - b.tierStart);
                            for (let i = 1; i < data.tiers.length; i++) {
                                if (data.tiers[i].tierStart <= data.tiers[i - 1].tierEnd) {
                                    data.tiers[i].tierStart = data.tiers[i - 1].tierEnd + 1;
                                }
                            }
                            
                            const jsonStr = JSON.stringify(data.tiers);
                            output += `${data.countryCode}\t${data.zone}\t${data.timeEffect}\t${data.minWeight}\t${data.maxWeight}\t${jsonStr}\n`;
                        }
                        
                        this.rymResult = output.trim();
                        
                        if (this.rymResult && !this.errors) {
                            this.$message({
                                message: "转换成功！",
                                type: "success",
                                duration: 1500
                            });
                        }
                        
                    } catch (error) {
                        this.errors = "转换过程中发生错误：" + error.message;
                        this.$message({
                            message: "转换失败，请检查数据格式",
                            type: "error"
                        });
                    } finally {
                        this.loading = false;
                    }
                },
                clearRym() {
                    this.rymInput = "";
                    this.rymResult = "";
                    this.errors = "";
                },
                copyRymResult() {
                    copyToClipboard(this.rymResult, this.$message);
                }
            }
        });
    </script>
</body>
</html>
