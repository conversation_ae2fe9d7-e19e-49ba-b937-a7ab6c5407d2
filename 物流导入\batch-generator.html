<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>批量文件生成</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 100%;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h3 {
        color: #2c3e50;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .form-section {
        margin-bottom: 20px;
      }
      .form-section label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #2c3e50;
      }
      .button-group {
        margin: 20px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
      .result-box {
        margin-top: 20px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .success-box {
        margin-top: 15px;
        padding: 15px;
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 6px;
        color: #0369a1;
      }
      .error-box {
        margin-top: 15px;
        padding: 15px;
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 6px;
        color: #c53030;
      }
      .file-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 10px;
        background: #f8f9fa;
      }
      .file-item {
        padding: 5px 0;
        border-bottom: 1px solid #e9ecef;
      }
      .file-item:last-child {
        border-bottom: none;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h3>📁 批量文件生成器</h3>
      </div>

      <!-- <div class="form-section">
            <label>模板文件名：</label>
            <el-input
                v-model="templateFileName"
                placeholder="请输入模板文件名，例如：template.txt"
            ></el-input>
        </div> -->

      <div class="form-section">
        <label>目标文件名列表（每行一个）：</label>
        <el-input
          type="textarea"
          :rows="8"
          v-model="fileNameList"
          placeholder="请输入要生成的文件名，每行一个，例如：&#10;file1.txt&#10;file2.txt&#10;file3.txt"
        ></el-input>
        <div style="margin-top: 8px; font-size: 12px; color: #666">
          <i class="el-icon-info"></i> 支持的模板文件格式：.txt, .html, .js,
          .css, .json, .xml, .md, .xlsx, .xls, .csv
        </div>
      </div>

      <div class="form-section">
        <label>输出文件夹名：</label>
        <el-input
          v-model="outputFolder"
          placeholder="请输入输出文件夹名，例如：generated_files"
        ></el-input>
      </div>

      <div
        style="
          margin-bottom: 15px;
          padding: 10px;
          background: #f0f9ff;
          border-left: 4px solid #409eff;
          font-size: 14px;
        "
      >
        <strong>使用说明：</strong><br />
        1. 点击"批量生成"按钮选择模板文件<br />
        2. 模板文件中可使用 filename 作为占位符，将被替换为实际文件名<br />
        3. 支持Excel文件(.xlsx/.xls)，会自动转换为CSV格式处理<br />
        4. 生成完成后可下载包含所有文件的ZIP压缩包
      </div>

      <div class="button-group">
        <el-button type="primary" @click="generateFiles" :loading="loading">
          <i class="el-icon-folder-add"></i> 批量生成
        </el-button>
        <el-button @click="clearForm">
          <i class="el-icon-delete"></i> 清空
        </el-button>
        <el-button
          @click="downloadZip"
          v-if="generatedFiles.length > 0"
          type="success"
        >
          <i class="el-icon-download"></i> 下载ZIP
        </el-button>
      </div>

      <div class="error-box" v-if="errorMessage">{{ errorMessage }}</div>

      <div class="success-box" v-if="successMessage">{{ successMessage }}</div>

      <div class="result-box" v-if="generatedFiles.length > 0">
        <h4>生成的文件列表：</h4>
        <div class="file-list">
          <div
            class="file-item"
            v-for="file in generatedFiles"
            :key="file.name"
          >
            📄 {{ file.name }} ({{ file.size }} bytes)
          </div>
        </div>
      </div>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jszip@3.7.1/dist/jszip.min.js"></script>
    <!-- 引入 SheetJS 库用于处理 Excel 文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            loading: false,
            fileNameList: "file1.txt\nfile2.txt\nfile3.txt",
            outputFolder: "generated_files",
            generatedFiles: [],
            errorMessage: "",
            successMessage: "",
          };
        },
        methods: {
          async generateFiles() {
            this.loading = true;
            this.errorMessage = "";
            this.successMessage = "";
            this.generatedFiles = [];

            try {
              // 验证输入

              if (!this.fileNameList.trim()) {
                throw new Error("请输入目标文件名列表");
              }

              if (!this.outputFolder.trim()) {
                throw new Error("请输入输出文件夹名");
              }

              // 读取模板文件内容
              const templateContent = await this.readTemplateFile();

              // 解析文件名列表
              const fileNames = this.fileNameList
                .trim()
                .split("\n")
                .map((name) => name.trim())
                .filter((name) => name.length > 0);

              if (fileNames.length === 0) {
                throw new Error("文件名列表为空");
              }

              // 生成文件
              for (const fileName of fileNames) {
                if (fileName) {
                  const fileContent = templateContent.replace(
                    /{{filename}}/g,
                    fileName
                  );
                  this.generatedFiles.push({
                    name: fileName,
                    content: fileContent,
                    size: new Blob([fileContent]).size,
                  });
                }
              }

              this.successMessage = `成功生成 ${this.generatedFiles.length} 个文件！`;

              this.$message({
                message: "文件生成完成！",
                type: "success",
                duration: 2000,
              });
            } catch (error) {
              this.errorMessage = error.message;
              this.$message({
                message: "生成失败：" + error.message,
                type: "error",
              });
            } finally {
              this.loading = false;
            }
          },

          async readTemplateFile() {
            // 模拟读取模板文件，实际应用中可能需要用户上传或选择文件
            return new Promise((resolve, reject) => {
              const input = document.createElement("input");
              input.type = "file";
              input.accept =
                ".txt,.html,.js,.css,.json,.xml,.md,.xlsx,.xls,.csv";

              input.onchange = (event) => {
                const file = event.target.files[0];
                if (!file) {
                  reject(new Error("未选择模板文件"));
                  return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                  try {
                    // 检查文件类型
                    const fileName = file.name.toLowerCase();
                    if (
                      fileName.endsWith(".xlsx") ||
                      fileName.endsWith(".xls")
                    ) {
                      // 处理Excel文件
                      const data = new Uint8Array(e.target.result);
                      const workbook = XLSX.read(data, { type: "array" });
                      const firstSheetName = workbook.SheetNames[0];
                      const worksheet = workbook.Sheets[firstSheetName];
                      const csvContent = XLSX.utils.sheet_to_csv(worksheet);
                      resolve(csvContent);
                    } else if (fileName.endsWith(".csv")) {
                      // 处理CSV文件
                      resolve(e.target.result);
                    } else {
                      // 处理文本文件
                      resolve(e.target.result);
                    }
                  } catch (error) {
                    reject(new Error("文件解析失败: " + error.message));
                  }
                };
                reader.onerror = () => {
                  reject(new Error("读取模板文件失败"));
                };

                // 根据文件类型选择读取方式
                const fileName = file.name.toLowerCase();
                if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                  reader.readAsArrayBuffer(file);
                } else {
                  reader.readAsText(file);
                }
              };

              input.click();
            });
          },

          async downloadZip() {
            try {
              const zip = new JSZip();
              const folder = zip.folder(this.outputFolder);

              // 添加所有生成的文件到ZIP
              this.generatedFiles.forEach((file) => {
                folder.file(file.name, file.content);
              });

              // 生成ZIP文件
              const content = await zip.generateAsync({ type: "blob" });

              // 下载ZIP文件
              const url = window.URL.createObjectURL(content);
              const a = document.createElement("a");
              a.href = url;
              a.download = `${this.outputFolder}.zip`;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
              window.URL.revokeObjectURL(url);

              this.$message({
                message: "ZIP文件下载开始！",
                type: "success",
              });
            } catch (error) {
              this.$message({
                message: "下载失败：" + error.message,
                type: "error",
              });
            }
          },

          clearForm() {
            this.fileNameList = "";
            this.outputFolder = "";
            this.generatedFiles = [];
            this.errorMessage = "";
            this.successMessage = "";
          },
        },
      });
    </script>
  </body>
</html>
