<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>物流工具箱</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h1 {
        color: #2c3e50;
        margin: 0;
        font-size: 28px;
        font-weight: 600;
      }
      .tab-content {
        width: 100%;
        height: 650px;
        border: none;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .loading {
        text-align: center;
        padding: 50px;
        color: #666;
        font-size: 16px;
      }
      .el-tabs__header {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 20px;
      }
      .el-tabs__nav-wrap::after {
        display: none;
      }
      .el-tabs__item {
        font-weight: 500;
        font-size: 14px;
      }
      .el-tabs__item.is-active {
        color: #409eff;
        font-weight: 600;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h1>📦 物流工具箱</h1>
      </div>

      <!-- Tab 切换 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <!-- Tab 1: 阶梯价格转换 -->
        <el-tab-pane label="阶梯价格转换" name="tier">
          <iframe
            v-if="activeTab === 'tier'"
            src="tier-price.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>

        <!-- Tab 2: 尺寸转换 -->
        <el-tab-pane label="尺寸转换" name="dimension">
          <iframe
            v-if="activeTab === 'dimension'"
            src="dimension.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>

        <!-- Tab 3: 云途格式转换 -->
        <el-tab-pane label="云途格式转换" name="yuntu">
          <iframe
            v-if="activeTab === 'yuntu'"
            src="yuntu-converter.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>

        <!-- Tab 4: 燕文格式转换 -->
        <el-tab-pane label="燕文格式转换" name="yanwen">
          <iframe
            v-if="activeTab === 'yanwen'"
            src="yanwen-converter.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>

        <!-- Tab 5: RYM格式转换 -->
        <el-tab-pane label="RYM格式转换" name="rym">
          <iframe
            v-if="activeTab === 'rym'"
            src="rym-converter.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>

        <!-- Tab 6: 批量文件生成 -->
        <el-tab-pane label="批量文件生成" name="batch">
          <iframe
            v-if="activeTab === 'batch'"
            src="batch-generator.html"
            class="tab-content"
            frameborder="0"
          >
          </iframe>
          <div v-else class="loading">加载中...</div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            activeTab: "tier",
          };
        },
        methods: {
          handleTabClick(tab) {
            console.log("切换到标签页:", tab.name);
          },
        },
      });
    </script>
  </body>
</html>
