<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>尺寸转换</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 100%;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h3 {
        color: #2c3e50;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .input-area {
        margin-bottom: 20px;
      }
      .result-box {
        margin-top: 20px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        margin: 0;
        font-family: "Consolas", "Monaco", monospace;
      }
      .button-group {
        margin: 15px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h3>📏 尺寸转换</h3>
      </div>

      <div class="input-area">
        <el-input
          v-model="dimensionInput"
          type="textarea"
          :rows="3"
          placeholder="请输入尺寸，例如：60*40*35 或 60cm*40cm*35cm"
        ></el-input>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="convertDimension">
          <i class="el-icon-refresh"></i> 转换
        </el-button>
        <el-button @click="clearDimension">
          <i class="el-icon-delete"></i> 清空
        </el-button>
        <el-button
          @click="copyDimensionResult"
          v-if="dimensionResult"
          type="success"
        >
          <i class="el-icon-document-copy"></i> 复制结果
        </el-button>
      </div>

      <div class="result-box" v-if="dimensionResult">
        <pre>{{ dimensionResult }}</pre>
      </div>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="common.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            dimensionInput: "",
            dimensionResult: "",
          };
        },
        methods: {
          convertDimension() {
            const input = this.dimensionInput.trim();
            if (!input) {
              this.$message({ message: "请输入尺寸", type: "warning" });
              return;
            }

            // 移除单位 cm/Cm/cM 等
            const cleaned = input.replace(/cm/gi, "").replace(/\s+/g, "");
            const parts = cleaned.split(/[*xX]/);

            if (parts.length !== 3) {
              this.$message({
                message: "请输入正确的三边尺寸，例如：60*40*35",
                type: "error",
              });
              return;
            }

            const nums = parts.map((s) => {
              const n = parseFloat(s.trim());
              return isNaN(n) ? null : Math.round(n);
            });

            if (nums.some((n) => n === null)) {
              this.$message({ message: "尺寸中包含无效数字", type: "error" });
              return;
            }

            const [l, w, h] = nums;
            const maxSingleSide = Math.max(l, w, h);

            const result = {
              maxLength: l,
              maxWidth: w,
              maxHeight: h,
              maxSingleSide: maxSingleSide,
            };

            this.dimensionResult = JSON.stringify(result);
          },
          clearDimension() {
            this.dimensionInput = "";
            this.dimensionResult = "";
          },
          copyDimensionResult() {
            copyToClipboard(this.dimensionResult, this.$message);
          },
        },
      });
    </script>
  </body>
</html>
