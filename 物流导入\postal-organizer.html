<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>邮编分区整理</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 100%;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h3 {
        color: #2c3e50;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .input-area {
        margin-bottom: 20px;
      }
      .result-box {
        margin-top: 20px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        margin: 0;
        font-family: "Consolas", "Monaco", monospace;
        max-height: 400px;
        overflow-y: auto;
      }
      .button-group {
        margin: 15px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
      .hidden {
        display: none !important;
      }
      .zone-result {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #f8f9fa;
      }
      .zone-title {
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 16px;
      }
      .zone-content {
        font-family: "Consolas", "Monaco", monospace;
        background: #fff;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
        word-break: break-all;
      }
      .example-text {
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;
        line-height: 1.5;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h3>📮 邮编分区整理</h3>
      </div>

      <div class="example-text">
        <strong>使用说明：</strong><br>
        1. 输入格式：每行一个邮编和分区，用制表符或空格分隔，如：9700 1区<br>
        2. 系统会自动按分区分组，并将邮编排序后转换为JSON格式<br>
        3. 支持单个邮编和邮编范围的智能识别
      </div>

      <div class="input-area">
        <el-input
          type="textarea"
          :rows="8"
          placeholder="请输入邮编数据，格式：9700	1区（每行一个）"
          v-model="postalInput"
        ></el-input>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="organizePostal">
          <i class="el-icon-refresh"></i> 整理分区
        </el-button>
        <el-button @click="clearPostal">
          <i class="el-icon-delete"></i> 清空
        </el-button>
        <el-button @click="toggleFormat" v-if="postalResult.length > 0" type="info">
          <i class="el-icon-view"></i> {{ isPretty ? '紧凑格式' : '美化格式' }}
        </el-button>
        <el-button @click="copyAllResults" v-if="postalResult.length > 0" type="success">
          <i class="el-icon-document-copy"></i> 复制所有结果
        </el-button>
      </div>

      <div v-if="postalResult.length > 0">
        <div v-for="zone in postalResult" :key="zone.name" class="zone-result">
          <div class="zone-title">{{ zone.name }}</div>
          <div class="zone-content">
            <span v-if="!isPretty">{{ zone.compact }}</span>
            <pre v-else>{{ zone.pretty }}</pre>
          </div>
          <div style="margin-top: 10px;">
            <el-button size="mini" @click="copyZoneResult(zone)">
              <i class="el-icon-document-copy"></i> 复制此分区
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="common.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            postalInput: `9700	1区
9701	1区
9702	1区
9703	1区
9704	1区
9705	1区
9706	1区
9707	1区
9708	1区
9709	1区
9710	1区
9711	1区
9712	1区
9713	1区
9714	1区
9715	1区
9716	1区
9717	1区
9718	1区
9719	1区
9720	1区
9721	1区
9722	1区
9723	1区
9724	1区
9725	1区
9999	1区
0200	2区
0201	2区
0202	2区
0203	2区
0204	2区
0205	2区
0206	2区
0207	2区
0208	2区
0209	2区
0210	2区
0211	2区
0212	2区
0213	2区
0214	2区
0215	2区
0216	2区
0217	2区
0218	2区
0219	2区
0220	2区
0221	2区
0222	2区
0223	2区
0224	2区
0225	2区
0226	2区
0227	2区
0228	2区
0229	2区
0230	2区
0231	2区
0232	2区
0233	2区
0234	2区`,
            postalResult: [],
            isPretty: false,
          };
        },
        methods: {
          organizePostal() {
            const lines = this.postalInput.trim().split("\n");
            if (lines.length === 0) return;

            // 按分区分组
            const zones = {};
            
            for (const line of lines) {
              const parts = line.trim().split(/\s+/).filter(Boolean);
              if (parts.length < 2) continue;
              
              const postalCode = parts[0];
              const zone = parts[1];
              
              if (!zones[zone]) {
                zones[zone] = [];
              }
              zones[zone].push(postalCode);
            }

            // 处理每个分区
            this.postalResult = [];
            
            Object.keys(zones).sort().forEach(zoneName => {
              const codes = zones[zoneName];
              const result = this.processPostalCodes(codes);
              
              this.postalResult.push({
                name: zoneName,
                compact: JSON.stringify(result),
                pretty: JSON.stringify(result, null, 2)
              });
            });
          },
          
          processPostalCodes(codes) {
            // 排序邮编
            const sortedCodes = codes.sort((a, b) => {
              // 转换为数字进行比较，处理前导零
              return parseInt(a) - parseInt(b);
            });
            
            const result = [];
            let i = 0;
            
            while (i < sortedCodes.length) {
              const start = sortedCodes[i];
              let end = start;
              let j = i + 1;
              
              // 查找连续的邮编
              while (j < sortedCodes.length) {
                const current = parseInt(sortedCodes[j]);
                const prev = parseInt(sortedCodes[j - 1]);
                
                if (current === prev + 1) {
                  end = sortedCodes[j];
                  j++;
                } else {
                  break;
                }
              }
              
              // 决定是单个邮编还是范围
              if (start === end) {
                result.push(start);
              } else {
                // 如果只有两个连续邮编，分别列出
                if (parseInt(end) - parseInt(start) === 1) {
                  result.push(start);
                  result.push(end);
                } else {
                  // 三个或以上连续邮编，使用范围格式
                  result.push(`${start}-${end}`);
                }
              }
              
              i = j;
            }
            
            return result;
          },
          
          clearPostal() {
            this.postalInput = "";
            this.postalResult = [];
          },
          
          toggleFormat() {
            this.isPretty = !this.isPretty;
          },
          
          copyZoneResult(zone) {
            const text = this.isPretty ? zone.pretty : zone.compact;
            copyToClipboard(text, this.$message);
          },
          
          copyAllResults() {
            let allText = "";
            this.postalResult.forEach(zone => {
              const text = this.isPretty ? zone.pretty : zone.compact;
              allText += `// ${zone.name}\n${text}\n\n`;
            });
            copyToClipboard(allText.trim(), this.$message);
          },
        },
      });
    </script>
  </body>
</html>
