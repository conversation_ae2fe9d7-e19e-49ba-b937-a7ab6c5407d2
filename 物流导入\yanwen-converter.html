<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>燕文格式转换</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 100%;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h3 {
        color: #2c3e50;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .input-area {
        margin-bottom: 20px;
      }
      .result-box {
        margin-top: 20px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        margin: 0;
        font-family: "Consolas", "Monaco", monospace;
      }
      .example-text {
        color: #6c757d;
        font-size: 13px;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #28a745;
      }
      .button-group {
        margin: 15px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
      .error-box {
        margin-top: 15px;
        padding: 15px;
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 6px;
        color: #c53030;
        white-space: pre-line;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h3>🌟 燕文格式转换</h3>
      </div>

      <div class="example-text">
        📋 支持两种格式：<br />
        1. 国家 | CountryCode | 公斤运费(元/KG) | 处理费(元/件) | 重量段(KG) |
        最小计费重量(KG)<br />
        2. 国家 | 分区 | 公斤运费(元/KG) | 处理费(元/件) | 重量段(KG) |
        最小计费重量(KG)
      </div>

      <div class="input-area">
        <el-input
          type="textarea"
          :rows="8"
          placeholder="请粘贴燕文Excel数据，包含表头行"
          v-model="yanwenInput"
        ></el-input>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="convertYanwen" :loading="loading">
          <i class="el-icon-refresh"></i> 转换
        </el-button>
        <el-button @click="clearYanwen">
          <i class="el-icon-delete"></i> 清空
        </el-button>
        <el-button @click="copyYanwenResult" v-if="yanwenResult" type="success">
          <i class="el-icon-document-copy"></i> 复制结果
        </el-button>
      </div>

      <div class="error-box" v-if="errors">{{ errors }}</div>

      <div class="result-box" v-if="yanwenResult">
        <pre>{{ yanwenResult }}</pre>
      </div>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="common.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            loading: false,
            errors: "",
            yanwenInput: `国家	CountryCode	公斤运费(元/KG)	处理费(元/件)	重量段(KG)	最小计费重量(KG)
美国	US	97	25	0.001 - 0.11	0.05
美国	US	97	25	0.111 - 0.22	0.05
美国	US	95	27	0.221 - 0.34	0.05
加拿大	CA	86	28	0.001 - 0.3	0.05
英国	GB	73	18	0.001 - 1	0.001`,
            yanwenResult: "",
          };
        },
        methods: {
          convertYanwen() {
            this.loading = true;
            this.errors = "";
            this.yanwenResult = "";

            try {
              const lines = this.yanwenInput.trim().split("\n");
              if (lines.length === 0) {
                this.$message({ message: "请输入数据", type: "warning" });
                return;
              }

              // 跳过表头行
              const dataLines = lines.slice(1);
              const errors = [];
              const countryData = {};

              for (let i = 0; i < dataLines.length; i++) {
                const line = dataLines[i];
                const parts = line.trim().split(/\t/);
                const lineNumber = i + 2;

                if (parts.length < 5) {
                  errors.push(
                    `第${lineNumber}行：字段数量不足，需要至少5个字段`
                  );
                  continue;
                }

                let country,
                  countryCode,
                  zone,
                  unitPrice,
                  processingFee,
                  weightRange,
                  minChargeWeight;

                // 判断格式类型
                if (parts.length >= 6 && parts[1].match(/^[A-Z]{2}$/)) {
                  // 格式1: 国家 CountryCode 公斤运费 处理费 重量段 最小计费重量
                  country = parts[0].trim();
                  countryCode = parts[1].trim();
                  unitPrice = parseFloat(parts[2]);
                  processingFee = parseFloat(parts[3]);
                  weightRange = parts[4].trim();
                  minChargeWeight = parseFloat(parts[5]);
                  zone = "";
                } else {
                  // 格式2: 国家 分区 公斤运费 处理费 重量段 最小计费重量
                  country = parts[0].trim();
                  zone = parts[1].trim();
                  unitPrice = parseFloat(parts[2]);
                  processingFee = parseFloat(parts[3]);
                  weightRange = parts[4].trim();
                  minChargeWeight = parseFloat(parts[5]);

                  countryCode = COUNTRY_MAP[country] || country;
                  if (!COUNTRY_MAP[country]) {
                    errors.push(
                      `第${lineNumber}行：未识别的国家名称"${country}"`
                    );
                  }
                }

                if (
                  !countryCode ||
                  !weightRange ||
                  isNaN(unitPrice) ||
                  isNaN(processingFee)
                ) {
                  errors.push(`第${lineNumber}行：必填字段缺失或格式错误`);
                  continue;
                }

                // 解析重量范围 (例如: "0.001 - 0.11" 或 "0.001-0.11")
                const match = weightRange.match(/([\d.]+)\s*-\s*([\d.]+)/);
                if (!match) {
                  errors.push(
                    `第${lineNumber}行：重量范围格式不正确，应为"0.001 - 0.11"格式`
                  );
                  continue;
                }

                const startWeight = Math.round(parseFloat(match[1]) * 1000); // 转换为克
                const endWeight = Math.round(parseFloat(match[2]) * 1000);

                const key = zone ? `${countryCode}_${zone}` : countryCode;

                if (!countryData[key]) {
                  countryData[key] = {
                    countryCode: countryCode,
                    zone: zone,
                    timeEffect: "", // 默认时效
                    minWeight: startWeight,
                    maxWeight: endWeight,
                    tiers: [],
                  };
                }

                // 更新最小和最大重量
                countryData[key].minWeight = Math.min(
                  countryData[key].minWeight,
                  startWeight
                );
                countryData[key].maxWeight = Math.max(
                  countryData[key].maxWeight,
                  endWeight
                );

                // 添加阶梯价格
                const tierItem = {
                  tierStart: startWeight,
                  tierEnd: endWeight,
                  unitPrice: Math.round(unitPrice * 100), // 转换为分
                  registrationFee: Math.round(processingFee * 100), // 处理费作为挂号费
                };

                // 添加最低计费重
                if (!isNaN(minChargeWeight)) {
                  tierItem.minChargeWeight = Math.round(minChargeWeight * 1000);
                }

                countryData[key].tiers.push(tierItem);
              }

              if (errors.length > 0) {
                this.errors = formatErrors(errors);
              }

              // 生成输出
              let output = "";
              for (const [key, data] of Object.entries(countryData)) {
                // 修正阶梯边界
                data.tiers.sort((a, b) => a.tierStart - b.tierStart);
                for (let i = 1; i < data.tiers.length; i++) {
                  if (data.tiers[i].tierStart <= data.tiers[i - 1].tierEnd) {
                    data.tiers[i].tierStart = data.tiers[i - 1].tierEnd + 1;
                  }
                }

                const jsonStr = JSON.stringify(data.tiers);
                output += `${data.countryCode}\t${data.zone}\t${data.timeEffect}\t${data.minWeight}\t${data.maxWeight}\t${jsonStr}\n`;
              }

              this.yanwenResult = output.trim();

              if (this.yanwenResult && !this.errors) {
                this.$message({
                  message: "转换成功！",
                  type: "success",
                  duration: 1500,
                });
              }
            } catch (error) {
              this.errors = "转换过程中发生错误：" + error.message;
              this.$message({
                message: "转换失败，请检查数据格式",
                type: "error",
              });
            } finally {
              this.loading = false;
            }
          },
          clearYanwen() {
            this.yanwenInput = "";
            this.yanwenResult = "";
            this.errors = "";
          },
          copyYanwenResult() {
            copyToClipboard(this.yanwenResult, this.$message);
          },
        },
      });
    </script>
  </body>
</html>
