import shutil

# 定义源文件路径和目标文件名列表
source_file = 'yanwen_模板.xlsx'
file_names = [
    "1_燕文美国快线-普货_1034", "2_燕文美国快线-特货_1035",
    "3_燕文英国RM快线-普货_518", "4_燕文英国YODEL快线-普货_32",
    "5_燕文英国Evri快线-普货_1730", "6_燕文英国HERMES快线-普货_866",
    "7_燕文英国Hermes快线-特货_1277", "8_燕文德国快线-特货_1265",
    "9_燕文德国Hermes快线-普货_1700", "10_燕文英国Evri快线定制-特货_1754",
    "11_燕文德国快线-普货_810", "12_燕文法国快线-普货_779",
    "13_燕文澳洲快线-普货_842", "14_燕文澳洲快线-特货_1255",
    "15_燕文专线快递-普货_440", "16_燕文专线快递-特货_557",
    "17_燕文化妆品快递_1224", "18_燕文精品快递-普货_1605",
    "19_燕文精品快递-特货_1606", "20_燕文德国Hermes快线-特货_1701",
    "21_燕文东欧快线-普货_1794", "22_燕文东欧快线-特货_1795",
    "23_燕文美国快线普货-TI_1873", "24_燕文Aramex快递-普货_979",
    "25_燕文专线追踪-普货_481", "26_轻小件专线_801",
    "27_燕文专线追踪-特货_484", "28_燕文专线惠选-普货_995",
    "29_燕文化妆品专线_1046", "30_燕文服装专线-普货_1500",
    "31_燕文大货专线追踪-普货_1557", "32_燕文大货专线追踪-特货_1558",
    "33_燕文美国岛屿专线普货-TI_1568", "34_燕文美国岛屿专线特货-TI_1569",
    "35_燕文服装特惠专线-普货_1611", "36_燕文专线追踪-纯电_1703",
    "37_燕文专线追踪-敏感货_1704", "38_燕文专线追踪普货-TI_1848",
    "39_燕文专线追踪特货-TI_1849", "40_燕文专线化妆品-TI_1850",
    "41_燕文航空挂号-普货_437", "42_燕文航空挂号-特货_456",
    "43_燕文专线平邮小包-普货_526", "44_燕文专线平邮小包-特货_527",
    "45_燕文航空经济小包-普货_438", "46_燕文航空经济小包-特货_457",
    "47_郑州EMS_1225", "48_中邮上海E特快_156",
    "49_中邮上海线下E邮宝_155", "50_中邮郑州线下E邮宝_556",
    "51_中邮上海挂号小包_166", "52_香港DHL-美国标准_1291",
    "53_香港DHL_45", "54_香港DHL-美欧大货特价_1155",
    "55_大陆DHL_5", "56_深圳DHL_1741",
    "57_大陆UPS经济商业快递_1221", "58_香港UPS红单-欧美大货特价_1163",
    "59_大陆UPS红单5700_1317", "60_大陆UPS蓝单5700_1318",
    "61_香港UPS红单免杂_1713", "62_大陆UPS红单特货_1735",
    "63_大陆UPS蓝单6000_1199", "64_大陆UPS红单6000_1137",
    "65_大陆FEDEX-IP_733", "66_大陆Fedex-IE-郑州_1297"
]

# 复制文件
for file_name in file_names:
    target_file = f"{file_name}.xlsx"
    try:
        shutil.copy(source_file, target_file)
        print(f"成功复制文件: {target_file}")
    except FileNotFoundError:
        print(f"源文件不存在: {source_file}")
    except Exception as e:
        print(f"复制文件时出错: {e}")