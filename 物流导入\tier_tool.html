<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>物流工具箱</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background-color: #f5f7fa;
      }
      .container {
        max-width: 900px;
        margin: 0 auto;
      }
      .input-area {
        margin-bottom: 15px;
      }
      .result-box {
        margin-top: 15px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background-color: #fff;
        padding: 15px;
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        background: #f9f9f9;
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #ebeef5;
        margin: 0;
      }
      .hidden {
        display: none !important;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <h1 style="text-align: center; color: #333">📦 物流工具箱</h1>

      <!-- Tab 切换 -->
      <el-tabs v-model="activeTab">
        <!-- Tab 1: 阶梯价格转换 -->
        <el-tab-pane label="阶梯价格转换" name="tier">
          <div class="input-area">
            <el-input
              type="textarea"
              :rows="6"
              placeholder="请输入原始数据（格式：0＜W≤0.1	162	31）"
              v-model="tierInput"
            ></el-input>
          </div>

          <div style="margin: 10px 0">
            <el-button type="primary" @click="convertTier">转换</el-button>
            <el-button @click="clearTier">清空</el-button>
            <el-button @click="toggleTierFormat" v-if="tierResult"
              >美化结果</el-button
            >
            <el-button @click="copyTierResult" v-if="tierResult"
              >复制结果</el-button
            >
          </div>

          <div class="result-box" v-if="tierResult">
            <pre :class="{ hidden: !tierIsPretty }">{{ tierPrettyResult }}</pre>
            <div :class="{ hidden: tierIsPretty }">{{ tierResult }}</div>
          </div>
        </el-tab-pane>

        <!-- Tab 2: 尺寸转换 -->
        <el-tab-pane label="尺寸转换" name="dimension">
          <div class="input-area">
            <el-input
              v-model="dimensionInput"
              type="textarea"
              :rows="3"
              placeholder="请输入尺寸，例如：60*40*35 或 60cm*40cm*35cm"
            ></el-input>
          </div>

          <div style="margin: 10px 0">
            <el-button type="primary" @click="convertDimension">转换</el-button>
            <el-button @click="clearDimension">清空</el-button>
            <el-button @click="copyDimensionResult" v-if="dimensionResult"
              >复制结果</el-button
            >
          </div>

          <div class="result-box" v-if="dimensionResult">
            <pre>{{ dimensionResult }}</pre>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            activeTab: "tier",

            // Tab1: 阶梯价格
            tierInput: `0＜W≤0.1\t162\t31
0.1＜W≤0.2\t156\t31
0.2＜W≤0.3\t168\t33
0.3＜W≤0.45\t167\t33
0.45＜W≤2\t163\t56
2＜W≤3\t163\t56
3＜W≤30\t163\t56`,
            tierResult: "",
            tierPrettyResult: "",
            tierIsPretty: false,

            // Tab2: 尺寸转换
            dimensionInput: "",
            dimensionResult: "",
          };
        },
        methods: {
          // ========== Tab1: 阶梯价格 ==========
          convertTier() {
            const lines = this.tierInput.trim().split("\n");
            if (lines.length === 0) return;

            // 国家名称映射
            const countryMap = {
              // ========== 欧洲 ==========
              英国: "GB",
              大不列颠: "GB",
              联合王国: "GB",
              德国: "DE",
              德意志: "DE",
              法国: "FR",
              法兰西: "FR",
              意大利: "IT",
              西班牙: "ES",
              荷兰: "NL",
              尼德兰: "NL",
              比利时: "BE",
              瑞典: "SE",
              奥地利: "AT",
              丹麦: "DK",
              芬兰: "FI",
              爱尔兰: "IE",
              葡萄牙: "PT",
              波兰: "PL",
              捷克: "CZ",
              捷克共和国: "CZ",
              匈牙利: "HU",
              罗马尼亚: "RO",
              保加利亚: "BG",
              斯洛伐克: "SK",
              斯洛文尼亚: "SI",
              希腊: "GR",
              克罗地亚: "HR",
              立陶宛: "LT",
              拉脱维亚: "LV",
              爱沙尼亚: "EE",
              瑞士: "CH",
              挪威: "NO",
              卢森堡: "LU",
              冰岛: "IS",
              列支敦士登: "LI",
              马耳他: "MT",
              塞浦路斯: "CY",

              // ========== 北美 ==========
              美国: "US",
              合众国: "US",
              加拿大: "CA",
              墨西哥: "MX",

              // ========== 亚洲 ==========
              日本: "JP",
              韩国: "KR",
              大韩民国: "KR",
              新加坡: "SG",
              马来西亚: "MY",
              泰国: "TH",
              菲律宾: "PH",
              越南: "VN",
              印度: "IN",
              巴基斯坦: "PK",
              以色列: "IL",
              阿联酋: "AE",
              阿拉伯联合酋长国: "AE",
              沙特阿拉伯: "SA",
              土耳其: "TR",

              // ========== 大洋洲 ==========
              澳大利亚: "AU",
              澳洲: "AU",
              新西兰: "NZ",

              // ========== 南美 ==========
              巴西: "BR",
              阿根廷: "AR",
              智利: "CL",
              哥伦比亚: "CO",
              秘鲁: "PE",

              // ========== 非洲 ==========
              南非: "ZA",
              埃及: "EG",
              尼日利亚: "NG",
              肯尼亚: "KE",
              摩洛哥: "MA",

              // ========== 其他常用别名或旧称（可选）==========
              俄罗斯: "RU",
              俄国: "RU",
              乌克兰: "UA",
              白俄罗斯: "BY",
              哈萨克斯坦: "KZ",
              印度尼西亚: "ID",
              斯里兰卡: "LK",
              孟加拉国: "BD",
              伊拉克: "IQ",
              伊朗: "IR",
              古巴: "CU",
              牙买加: "JM",
              巴哈马: "BS",
            };

            const result = {}; // 存储每个国家的数据
            let defaultCountryLines = []; // 无国家的行，用于兼容旧格式

            for (const line of lines) {
              const parts = line.trim().split(/\s+/).filter(Boolean);
              if (parts.length < 3) continue;

              const firstPart = parts[0];
              let country = null;
              let dataStartIndex = 0;

              // 判断第一项是否为国家名
              if (countryMap[firstPart]) {
                country = countryMap[firstPart];
                dataStartIndex = 1;
              }

              // 提取数据部分
              const range = parts[dataStartIndex];
              const unitPrice = parseFloat(parts[dataStartIndex + 1]);
              const registrationFee = parseFloat(parts[dataStartIndex + 2]);

              if (!range || isNaN(unitPrice) || isNaN(registrationFee))
                continue;

              const match = range.match(/([\d.]+)＜W≤([\d.]+)/);
              if (!match) continue;

              let start = Math.round(parseFloat(match[1]) * 1000);
              let end = Math.round(parseFloat(match[2]) * 1000);

              const unitPriceCents = Math.round(unitPrice * 100);
              const registrationFeeCents = Math.round(registrationFee * 100);

              const obj = {
                tierStart: start,
                tierEnd: end,
                unitPrice: unitPriceCents,
                registrationFee: registrationFeeCents,
              };

              if (country) {
                if (!result[country]) result[country] = [];
                result[country].push(obj);
              } else {
                defaultCountryLines.push(obj);
              }
            }

            // 处理多国家：修正每个国家内部的边界
            Object.keys(result).forEach((country) => {
              const arr = result[country];
              for (let i = 1; i < arr.length; i++) {
                if (arr[i].tierStart <= arr[i - 1].tierEnd) {
                  arr[i].tierStart = arr[i - 1].tierEnd + 1;
                }
              }
            });

            // 处理默认国家（无国家名的行）
            if (defaultCountryLines.length > 0) {
              for (let i = 1; i < defaultCountryLines.length; i++) {
                if (
                  defaultCountryLines[i].tierStart <=
                  defaultCountryLines[i - 1].tierEnd
                ) {
                  defaultCountryLines[i].tierStart =
                    defaultCountryLines[i - 1].tierEnd + 1;
                }
              }
              // 使用空字符串作为“默认国家”键
              result[""] = defaultCountryLines;
            }

            // 生成最终输出字符串
            let output = "";
            Object.keys(result).forEach((country) => {
              const jsonStr = JSON.stringify(result[country]);
              if (country === "") {
                output += jsonStr; // 无国家时只输出 JSON 数组
              } else {
                // output += `${country}:${jsonStr}\n`; //带国家编码的格式
                output += `${jsonStr}\n`;
              }
            });

            this.tierResult = output.trim();
            this.tierPrettyResult = output.trim();
            this.tierIsPretty = false;
          },
          clearTier() {
            this.tierInput = "";
            this.tierResult = "";
            this.tierPrettyResult = "";
          },
          toggleTierFormat() {
            this.tierIsPretty = !this.tierIsPretty;
          },
          copyTierResult() {
            const text = this.tierIsPretty
              ? this.tierPrettyResult
              : this.tierResult;
            this.$copyText(text);
          },

          // ========== Tab2: 尺寸转换 ==========
          convertDimension() {
            const input = this.dimensionInput.trim();
            if (!input) {
              this.$message({ message: "请输入尺寸", type: "warning" });
              return;
            }

            // 移除单位 cm/Cm/cM 等
            const cleaned = input.replace(/cm/gi, "").replace(/\s+/g, "");
            const parts = cleaned.split(/[*xX]/);

            if (parts.length !== 3) {
              this.$message({
                message: "请输入正确的三边尺寸，例如：60*40*35",
                type: "error",
              });
              return;
            }

            const nums = parts.map((s) => {
              const n = parseFloat(s.trim());
              return isNaN(n) ? null : Math.round(n);
            });

            if (nums.some((n) => n === null)) {
              this.$message({ message: "尺寸中包含无效数字", type: "error" });
              return;
            }

            const [l, w, h] = nums;
            const maxSingleSide = Math.max(l, w, h);

            const result = {
              maxLength: l,
              maxWidth: w,
              maxHeight: h,
              maxSingleSide: maxSingleSide,
            };

            this.dimensionResult = JSON.stringify(result);
          },
          clearDimension() {
            this.dimensionInput = "";
            this.dimensionResult = "";
          },
          copyDimensionResult() {
            this.$copyText(this.dimensionResult);
          },

          // 全局复制方法
          $copyText(text) {
            const $temp = $("<textarea>")
              .val(text)
              .css({ position: "absolute", left: "-9999px" });
            $("body").append($temp);
            $temp.select();
            document.execCommand("copy");
            $temp.remove();
            this.$message({
              message: "已复制到剪贴板",
              type: "success",
              duration: 1500,
            });
          },
        },
      });
    </script>
  </body>
</html>
