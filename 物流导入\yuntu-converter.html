<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>云途格式转换</title>

    <!-- 引入 Element UI 样式 -->
    <link
      rel="stylesheet"
      href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"
    />
    <style>
      body {
        font-family: "Helvetica Neue", Arial, sans-serif;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        min-height: 100vh;
      }
      .container {
        max-width: 100%;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
      }
      .header {
        text-align: center;
        margin-bottom: 25px;
      }
      .header h3 {
        color: #2c3e50;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
      .input-area {
        margin-bottom: 20px;
      }
      .result-box {
        margin-top: 20px;
        border: 1px solid #e1e8ed;
        border-radius: 8px;
        background-color: #fff;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      pre {
        white-space: pre-wrap;
        word-wrap: break-word;
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        margin: 0;
        font-family: "Consolas", "Monaco", monospace;
      }
      .example-text {
        color: #6c757d;
        font-size: 13px;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007bff;
      }
      .button-group {
        margin: 15px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }
      .error-box {
        margin-top: 15px;
        padding: 15px;
        background: #fff5f5;
        border: 1px solid #fed7d7;
        border-radius: 6px;
        color: #c53030;
        white-space: pre-line;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="header">
        <h3>🚀 云途格式转换</h3>
      </div>

      <div class="example-text">
        📋 示例格式：国家/地区 | 参考时效 | 分区 | 重量(KG) | 进位制(KG) |
        最低计费重(KG) | 运费(RMB/KG) | 挂号费(RMB/票)
      </div>

      <div class="input-area">
        <el-input
          type="textarea"
          :rows="8"
          placeholder="请粘贴云途Excel数据，包含表头行"
          v-model="yuntuInput"
        ></el-input>
      </div>

      <div class="button-group">
        <el-button type="primary" @click="convertYuntu" :loading="loading">
          <i class="el-icon-refresh"></i> 转换
        </el-button>
        <el-button @click="clearYuntu">
          <i class="el-icon-delete"></i> 清空
        </el-button>
        <el-button @click="copyYuntuResult" v-if="yuntuResult" type="success">
          <i class="el-icon-document-copy"></i> 复制结果
        </el-button>
      </div>

      <div class="error-box" v-if="errors">{{ errors }}</div>

      <div class="result-box" v-if="yuntuResult">
        <pre>{{ yuntuResult }}</pre>
      </div>
    </div>

    <!-- 引入依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="common.js"></script>

    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            loading: false,
            errors: "",
            yuntuInput: `国家/地区	参考时效	分区	重量(KG)	进位制(KG)	最低计费重(KG)	运费(RMB/KG)	挂号费(RMB/票)
瑞典	6-10工作日		0＜W≤0.3			95	16
瑞典	6-10工作日		0.3＜W≤2			92	21
瑞典	6-10工作日		2＜W≤20			92	21
奥地利	6-10工作日		0＜W≤2			100	23
奥地利	6-10工作日		2＜W≤30			100	23`,
            yuntuResult: "",
          };
        },
        methods: {
          convertYuntu() {
            this.loading = true;
            this.errors = "";
            this.yuntuResult = "";

            try {
              const lines = this.yuntuInput.trim().split("\n");
              if (lines.length === 0) {
                this.$message({ message: "请输入数据", type: "warning" });
                return;
              }

              // 跳过表头行
              const dataLines = lines.slice(1);
              const errors = [];

              // 定义必需字段
              const requiredFields = [
                { name: "国家/地区", required: true },
                { name: "参考时效", required: true },
                { name: "分区", required: false },
                { name: "重量(KG)", required: true, type: "weight_range" },
                { name: "进位制(KG)", required: false, type: "number" },
                { name: "最低计费重(KG)", required: false, type: "number" },
                { name: "运费(RMB/KG)", required: true, type: "number" },
                { name: "挂号费(RMB/票)", required: true, type: "number" },
              ];

              const countryData = {};

              for (let i = 0; i < dataLines.length; i++) {
                const line = dataLines[i];
                const parts = line.trim().split(/\t/);
                const lineNumber = i + 2; // +2 because we skipped header and arrays are 0-indexed

                // 验证数据行
                const lineErrors = validateDataLine(
                  parts,
                  requiredFields,
                  lineNumber
                );
                if (lineErrors.length > 0) {
                  errors.push(...lineErrors);
                  continue;
                }

                const country = parts[0].trim();
                const timeEffect = parts[1].trim();
                const zone = parts[2].trim();
                const weightRange = parts[3].trim();
                const roundingUnit = parts[4].trim();
                const minChargeWeight = parts[5].trim();
                const unitPrice = parseFloat(parts[6]);
                const registrationFee = parseFloat(parts[7]);

                // 解析重量范围
                const match = weightRange.match(
                  /([\d.]+)[\s＜<≤<=]+W?[\s≤<=]+([\d.]+)/
                );
                if (!match) {
                  errors.push(
                    `第${lineNumber}行：重量范围格式不正确，应为"0＜W≤1"格式`
                  );
                  continue;
                }

                const startWeight = Math.round(parseFloat(match[1]) * 1000); // 转换为克
                const endWeight = Math.round(parseFloat(match[2]) * 1000);

                const countryCode = COUNTRY_MAP[country] || country;
                if (!COUNTRY_MAP[country]) {
                  errors.push(
                    `第${lineNumber}行：未识别的国家名称"${country}"`
                  );
                }

                // 使用国家+分区作为唯一键，确保每个分区生成独立记录
                const key = zone ? `${countryCode}_${zone}` : countryCode;

                if (!countryData[key]) {
                  countryData[key] = {
                    countryCode: countryCode,
                    zone: zone,
                    timeEffect: extractTimeEffect(timeEffect),
                    minWeight: startWeight,
                    maxWeight: endWeight,
                    tiers: [],
                  };
                }

                // 更新最小和最大重量
                countryData[key].minWeight = Math.min(
                  countryData[key].minWeight,
                  startWeight
                );
                countryData[key].maxWeight = Math.max(
                  countryData[key].maxWeight,
                  endWeight
                );

                // 添加阶梯价格
                const tierItem = {
                  tierStart: startWeight,
                  tierEnd: endWeight,
                  unitPrice: Math.round(unitPrice * 100), // 转换为分
                  registrationFee: Math.round(registrationFee * 100),
                };

                // 添加进位制和最低计费重（如果有值）
                if (roundingUnit && !isNaN(parseFloat(roundingUnit))) {
                  tierItem.roundingUnit = Math.round(
                    parseFloat(roundingUnit) * 1000
                  );
                }
                if (minChargeWeight && !isNaN(parseFloat(minChargeWeight))) {
                  tierItem.minChargeWeight = Math.round(
                    parseFloat(minChargeWeight) * 1000
                  );
                }

                countryData[key].tiers.push(tierItem);
              }

              if (errors.length > 0) {
                this.errors = formatErrors(errors);
              }

              // 生成输出
              let output = "";
              for (const [key, data] of Object.entries(countryData)) {
                // 修正阶梯边界
                data.tiers.sort((a, b) => a.tierStart - b.tierStart);
                for (let i = 1; i < data.tiers.length; i++) {
                  if (data.tiers[i].tierStart <= data.tiers[i - 1].tierEnd) {
                    data.tiers[i].tierStart = data.tiers[i - 1].tierEnd + 1;
                  }
                }

                const jsonStr = JSON.stringify(data.tiers);
                output += `${data.countryCode}\t${data.zone}\t${data.timeEffect}\t${data.minWeight}\t${data.maxWeight}\t${jsonStr}\n`;
              }

              this.yuntuResult = output.trim();

              if (this.yuntuResult && !this.errors) {
                this.$message({
                  message: "转换成功！",
                  type: "success",
                  duration: 1500,
                });
              }
            } catch (error) {
              this.errors = "转换过程中发生错误：" + error.message;
              this.$message({
                message: "转换失败，请检查数据格式",
                type: "error",
              });
            } finally {
              this.loading = false;
            }
          },
          clearYuntu() {
            this.yuntuInput = "";
            this.yuntuResult = "";
            this.errors = "";
          },
          copyYuntuResult() {
            copyToClipboard(this.yuntuResult, this.$message);
          },
        },
      });
    </script>
  </body>
</html>
